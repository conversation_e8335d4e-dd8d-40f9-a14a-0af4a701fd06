// ================================================================
//                    公式格式设置使用示例 (Equation Format Usage Examples)
// ================================================================

// 这个文件展示了如何在章节中使用 equation.typ 中定义的格式设定和工具函数
// 具体公式内容在章节中编写，只使用格式设定功能

// --- 基本使用方法 ---
/*
在章节文件开头添加：

#import "../settings/equation.typ": *
#init-equations()

*/

// --- 使用示例 ---

// 1. 使用工具函数简化数学符号
/*
偏微分方程：#pdiff("u", "t") + #pdiff("u", "x") = 0

二阶偏微分：#pdiff2("u", "x") = 0

混合偏微分：#pdiff-mixed("u", "x", "y") = 0

应变率张量：#strain-rate-symbol("i", "j") = $frac(1, 2) (partial u_i / partial x_j + partial u_j / partial x_i)$

Reynolds应力：#reynolds-stress-symbol("i", "j") = $2 mu_T S_(i j) - frac(2, 3) rho k delta_(i j)$
*/

// 2. 使用公式格式化工具
/*
// 带标签的公式
#numbered-equation($frac(partial u, partial t) + u frac(partial u, partial x) = 0$, <eq:advection>)

// 公式说明文字
#equation-description[
  其中 $u$ 为速度，$t$ 为时间，$x$ 为空间坐标。
]

// 公式组
#equation-group(
  $frac(partial rho, partial t) + frac(partial (rho u), partial x) = 0$,
  $frac(partial (rho u), partial t) + frac(partial (rho u^2 + p), partial x) = 0$,
  $frac(partial E, partial t) + frac(partial ((E + p) u), partial x) = 0$
)
*/

// 3. 标准公式编写方式（推荐）
/*
Reynolds应力方程为：

$
  -rho overline(u'_i u'_j) = 2 mu_T S_(i j) - frac(2, 3) rho k delta_(i j)
$ <eq:reynolds-stress>

#equation-description[
  #mu-t-symbol() = $C_mu rho k^2 / epsilon$ 为湍流粘性系数，#strain-rate-symbol("i", "j") 为应变率张量。
]

湍动能输运方程为：

$
  frac(partial k, partial t) + u_j frac(partial k, partial x_j) = frac(partial, partial x_j) ( frac(nu_T, sigma_k) frac(partial k, partial x_j) ) + G_k - epsilon
$ <eq:k-transport>

耗散率输运方程为：

$
  frac(partial epsilon, partial t) + u_j frac(partial epsilon, partial x_j) = frac(partial, partial x_j) ( frac(nu_T, sigma_epsilon) frac(partial epsilon, partial x_j) ) + frac(epsilon, k) ( C_1 G_k - C_2 epsilon )
$ <eq:epsilon-transport>

#equation-description[
  湍动能生成项 $G_k = 2 mu_T S_(i j) S_(i j)$。
]
*/

// 4. 使用模板函数
/*
// 连续性方程
#continuity-template((rho: $rho$, u: $u$, x: $x$)) <eq:continuity>

// 输运方程
#transport-template($T$, source: $S_T$) <eq:temperature>

其中 $T$ 为温度，$S_T$ 为温度源项。
*/

// 5. 公式引用
/*
如方程 @eq:reynolds-stress 所示...
根据公式 @eq:k-transport 和 @eq:epsilon-transport...
*/

// --- 在章节中的完整使用示例 ---
/*
// 在章节文件开头
#import "../settings/heading.typ": *
#import "../settings/table.typ": styled-table, load-csv-simple, simple-table
#import "../settings/equation.typ": *

// 初始化公式设置
#init-equations()

#new-chapter()
#chapter[第三章 数值方法]

#section[控制方程]

本章采用雷诺平均Navier-Stokes方程组描述湍流流动。对于不可压缩流体，连续性方程为：

$
  frac(partial u_i, partial x_i) = 0
$ <eq:continuity>

动量方程为：

$
  frac(partial u_i, partial t) + u_j frac(partial u_i, partial x_j) = -frac(1, rho) frac(partial p, partial x_i) + nu frac(partial^2 u_i, partial x_j partial x_j) + frac(partial, partial x_j) (#reynolds-stress-symbol("i", "j"))
$ <eq:momentum>

#subsection[湍流模型]

采用Realizable k-ε湍流模型封闭Reynolds应力项：

$
  #reynolds-stress-symbol("i", "j") = 2 #mu-t-symbol() #strain-rate-symbol("i", "j") - frac(2, 3) rho #k-symbol() #kronecker("i", "j")
$ <eq:reynolds-closure>

#equation-description[
  #mu-t-symbol() = $C_mu rho k^2 / epsilon$ 为湍流粘性系数。
]

湍动能和耗散率的输运方程为：

$
  #pdiff("k", "t") + u_j #pdiff("k", "x_j") = #pdiff("", "x_j") ( frac(nu_T, sigma_k) #pdiff("k", "x_j") ) + G_k - epsilon
$ <eq:k-equation>

$
  #pdiff("epsilon", "t") + u_j #pdiff("epsilon", "x_j") = #pdiff("", "x_j") ( frac(nu_T, sigma_epsilon) #pdiff("epsilon", "x_j") ) + frac(epsilon, k) ( C_1 G_k - C_2 epsilon )
$ <eq:epsilon-equation>

数值求解方面，采用有限体积法离散控制方程...

*/

// ================================================================
//                    公式设置使用示例 (Equation Usage Examples)
// ================================================================

// 这个文件展示了如何在章节中使用 equation.typ 中定义的函数

// --- 基本使用方法 ---
/*
在章节文件开头添加：

#import "../settings/equation.typ": *
#init-equations()

*/

// --- 使用示例 ---

// 1. 使用预定义的湍流模型公式
/*
本研究采用k-ε湍流模型：

#k-epsilon-model()

这会自动生成完整的k-ε模型描述，包括所有相关公式和说明文字。
*/

// 2. 使用单独的公式函数
/*
Reynolds应力方程为：
#reynolds-stress-equation() <eq:reynolds>

湍动能方程为：
#k-equation() <eq:k>

耗散率方程为：
#epsilon-equation() <eq:epsilon>
*/

// 3. 使用数学符号简化函数
/*
偏微分方程：#pdiff("u", "t") + #pdiff("u", "x") = 0

应变率张量：#strain-rate("i", "j")

湍流粘性系数：#turbulent-viscosity()

湍动能生成项：#turbulent-production()
*/

// 4. 自定义公式的推荐写法
/*
对于不在预定义函数中的公式，建议这样写：

$
  frac(partial T, partial t) + u_j frac(partial T, partial x_j) = frac(partial, partial x_j) ( frac(alpha_T, sigma_T) frac(partial T, partial x_j) ) + S_T
$ <eq:temperature>

其中 $T$ 为温度，$alpha_T$ 为湍流热扩散系数，$S_T$ 为温度源项。
*/

// 5. 公式引用
/*
如方程 @eq:reynolds 所示...
根据公式 @eq:k-transport 和 @eq:epsilon-transport...
*/

// --- 在章节中的完整使用示例 ---
/*
// 在章节文件开头
#import "../settings/heading.typ": *
#import "../settings/table.typ": styled-table, load-csv-simple, simple-table
#import "../settings/equation.typ": *

// 初始化公式设置
#init-equations()

#new-chapter()
#chapter[第三章 数值方法]

#section[控制方程]

本章采用雷诺平均Navier-Stokes方程组描述湍流流动。对于不可压缩流体，连续性方程为：

$
  frac(partial u_i, partial x_i) = 0
$ <eq:continuity>

动量方程为：

$
  frac(partial u_i, partial t) + u_j frac(partial u_i, partial x_j) = -frac(1, rho) frac(partial p, partial x_i) + nu frac(partial^2 u_i, partial x_j partial x_j) + frac(partial, partial x_j) (-overline(u'_i u'_j))
$ <eq:momentum>

#subsection[湍流模型]

采用Realizable k-ε湍流模型封闭Reynolds应力项：

#k-epsilon-model()

数值求解方面，采用有限体积法离散控制方程...

*/

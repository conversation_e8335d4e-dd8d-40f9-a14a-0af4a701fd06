// ================================================================
//                    公式格式设置使用示例 (Equation Format Usage Examples)
// ================================================================

// 这个文件展示了如何在章节中使用新的公式格式设置系统
// 重要：具体公式内容在章节中编写，equation.typ 只提供格式和工具函数

// --- 基本使用方法 ---
/*
在章节文件开头添加：

#import "../settings/equation.typ": *
#init-equations()

*/

// --- 使用示例 ---

// 1. 标准公式编写方式（推荐）
/*
本研究采用Realizable k-ε湍流模型。Reynolds应力封闭为：

$
  -rho overline(u'_i u'_j) = 2 mu_T S_(i j) - frac(2, 3) rho k delta_(i j)
$ <eq:reynolds-stress>

#equation-description[
  其中 #mu-t-symbol() = $C_mu rho k^2 / epsilon$ 为湍流粘性系数，#strain-rate-symbol("i", "j") 为应变率张量。
]
*/

// 2. 使用工具函数简化符号输入
/*
湍动能和耗散率的输运方程为：

$
  #pdiff("k", "t") + u_j #pdiff("k", "x_j") = #pdiff("", "x_j") ( frac(nu_T, sigma_k) #pdiff("k", "x_j") ) + G_k - epsilon
$ <eq:k-transport>

$
  #pdiff("epsilon", "t") + u_j #pdiff("epsilon", "x_j") = #pdiff("", "x_j") ( frac(nu_T, sigma_epsilon) #pdiff("epsilon", "x_j") ) + frac(epsilon, k) ( C_1 G_k - C_2 epsilon )
$ <eq:epsilon-transport>
*/

// 3. 使用符号函数保持一致性
/*
在说明文字中使用符号函数：

#equation-description[
  湍动能生成项 $G_k = 2 #mu-t-symbol() #strain-rate-symbol("i", "j") #strain-rate-symbol("i", "j")$。
]
*/

// 4. 复杂公式组的处理
/*
对于一组相关的公式：

连续性方程：
$
  #pdiff("rho", "t") + #pdiff("(rho u_i)", "x_i") = 0
$ <eq:continuity>

动量方程：
$
  #pdiff("(rho u_i)", "t") + #pdiff("(rho u_i u_j)", "x_j") = -#pdiff("p", "x_i") + #pdiff("tau_(i j)", "x_j") + rho g_i
$ <eq:momentum>

#equation-description[
  其中 $tau_(i j)$ 为粘性应力张量，$g_i$ 为重力加速度分量。
]
*/

// --- 在章节中的完整使用示例 ---
/*
// 在章节文件开头
#import "../settings/heading.typ": *
#import "../settings/table.typ": styled-table, load-csv-simple, simple-table
#import "../settings/equation.typ": *

// 初始化公式设置
#init-equations()

#new-chapter()
#chapter[第三章 数值方法]

#section[控制方程]

本章采用雷诺平均Navier-Stokes方程组描述湍流流动。对于不可压缩流体，连续性方程为：

$
  #pdiff("u_i", "x_i") = 0
$ <eq:continuity>

动量方程为：

$
  #pdiff("u_i", "t") + u_j #pdiff("u_i", "x_j") = -frac(1, rho) #pdiff("p", "x_i") + nu #pdiff2("u_i", "x_j") + frac(1, rho) #pdiff("(-rho overline(u'_i u'_j))", "x_j")
$ <eq:momentum>

#subsection[湍流模型]

采用Realizable k-ε湍流模型封闭Reynolds应力项：

$
  #reynolds-stress-symbol("i", "j") = 2 #mu-t-symbol() #strain-rate-symbol("i", "j") - frac(2, 3) rho #k-symbol() #kronecker("i", "j")
$ <eq:reynolds-closure>

#equation-description[
  #mu-t-symbol() = $C_mu rho k^2 / epsilon$ 为湍流粘性系数，#strain-rate-symbol("i", "j") = $frac(1, 2) (partial u_i / partial x_j + partial u_j / partial x_i)$ 为应变率张量。
]

湍动能和耗散率的输运方程为：

$
  #pdiff("k", "t") + u_j #pdiff("k", "x_j") = #pdiff("", "x_j") ( frac(nu_T, sigma_k) #pdiff("k", "x_j") ) + G_k - epsilon
$ <eq:k-equation>

$
  #pdiff("epsilon", "t") + u_j #pdiff("epsilon", "x_j") = #pdiff("", "x_j") ( frac(nu_T, sigma_epsilon) #pdiff("epsilon", "x_j") ) + frac(epsilon, k) ( C_1 G_k - C_2 epsilon )
$ <eq:epsilon-equation>

#equation-description[
  湍动能生成项 $G_k = 2 #mu-t-symbol() #strain-rate-symbol("i", "j") #strain-rate-symbol("i", "j")$。
]

数值求解方面，采用有限体积法离散控制方程...

*/

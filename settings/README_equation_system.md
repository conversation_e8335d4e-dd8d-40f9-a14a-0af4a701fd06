# 公式设置系统使用指南

## 概述

这个公式设置系统通过 `settings/equation.typ` 文件提供了统一的公式管理功能，让您可以在章节文件中使用简洁的函数调用来生成复杂的数学公式和说明文字。

## 主要优势

1. **统一管理**: 所有公式样式和编号格式在一个文件中统一设置
2. **简化使用**: 章节文件中只需要简单的函数调用
3. **一致性**: 确保整个文档中公式格式的一致性
4. **易于维护**: 修改公式样式时只需要修改一个文件
5. **可重用**: 常用公式可以在多个章节中重复使用

## 基本使用方法

### 1. 在章节文件开头导入和初始化

```typst
#import "../settings/equation.typ": *
#init-equations()
```

### 2. 使用预定义的公式函数

```typst
// 完整的k-ε湍流模型
#k-epsilon-model()

// 单独的公式
#reynolds-stress-equation() <eq:reynolds>
#k-equation() <eq:k>
#epsilon-equation() <eq:epsilon>
```

### 3. 使用数学符号简化函数

```typst
// 偏微分
#pdiff("u", "t")

// 应变率张量
#strain-rate("i", "j")

// 湍流粘性系数
#turbulent-viscosity()
```

## 可用函数列表

### 初始化函数
- `init-equations()`: 初始化公式编号和样式设置

### 公式编号设置
- `setup-equation-numbering()`: 设置章节.序号格式的编号
- `setup-equation-style()`: 设置公式基本样式

### 数学符号函数
- `pdiff(f, x)`: 偏微分符号 ∂f/∂x
- `diff(f, x)`: 全微分符号 df/dx
- `vec(x)`: 向量符号
- `tensor(x)`: 张量符号
- `mat(x)`: 矩阵符号

### 流体力学专用函数
- `reynolds-stress(i, j)`: Reynolds应力项
- `strain-rate(i, j)`: 应变率张量
- `turbulent-viscosity()`: 湍流粘性系数
- `turbulent-production()`: 湍动能生成项

### 预定义公式函数
- `k-equation()`: k方程
- `epsilon-equation()`: ε方程
- `reynolds-stress-equation()`: Reynolds应力方程
- `k-epsilon-model()`: 完整的k-ε模型（包含说明文字）

### 工具函数
- `restore-paragraph()`: 恢复段落首行缩进
- `labeled-equation(eq, label)`: 带标签的公式
- `eq-ref(label)`: 公式引用

## 公式编号格式

公式会自动按照 "(章节号.序号)" 的格式编号，例如：
- 第2章的第1个公式: (2.1)
- 第2章的第2个公式: (2.2)
- 第3章的第1个公式: (3.1)

## 使用示例

### 完整的章节示例

```typst
#import "../settings/heading.typ": *
#import "../settings/equation.typ": *

#init-equations()

#new-chapter()
#chapter[第三章 数值方法]

#section[湍流模型]

本研究采用Realizable k-ε湍流模型：

#k-epsilon-model()

数值求解方面，采用SIMPLE算法求解压力-速度耦合...
```

### 自定义公式示例

```typst
对于温度方程：

$
  frac(partial T, partial t) + u_j frac(partial T, partial x_j) = frac(partial, partial x_j) ( frac(alpha_T, sigma_T) frac(partial T, partial x_j) ) + S_T
$ <eq:temperature>

其中 $T$ 为温度，$alpha_T$ 为湍流热扩散系数。
```

## 扩展和自定义

如果需要添加新的公式函数，可以在 `settings/equation.typ` 文件中添加：

```typst
// 添加新的公式函数
#let your-custom-equation() = {
  $your-equation-here$
}

// 添加新的符号函数
#let your-symbol(x) = $your-symbol-definition$
```

## 注意事项

1. 确保在每个使用公式的章节文件开头调用 `#init-equations()`
2. 公式标签使用 `<eq:name>` 格式，便于引用
3. 复杂公式建议创建专用函数，简单公式可以直接写在章节中
4. 修改公式样式时，只需要修改 `settings/equation.typ` 文件

## 文件结构

```
settings/
├── equation.typ                 # 主要的公式设置文件
├── equation_usage_examples.typ  # 使用示例
└── README_equation_system.md    # 本说明文件
```

这个系统让您可以专注于内容创作，而不需要担心公式格式的一致性问题。

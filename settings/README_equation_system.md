# 公式格式设置系统使用指南

## 概述

这个公式格式设置系统通过 `settings/equation.typ` 文件提供了统一的公式格式管理功能。**重要设计理念**：该文件只负责格式设定和工具函数，具体公式内容仍在各章节中单独编写，确保灵活性和独立性。

## 主要优势

1. **格式统一**: 所有公式样式和编号格式在一个文件中统一设置
2. **内容独立**: 具体公式内容在各章节中编写，避免单点故障
3. **工具丰富**: 提供常用数学符号和格式化工具函数
4. **易于维护**: 修改格式时只需要修改一个文件，不影响内容
5. **风险分散**: 避免所有公式集中在一个文件导致的风险

## 基本使用方法

### 1. 在章节文件开头导入和初始化

```typst
#import "../settings/equation.typ": *
#init-equations()
```

### 2. 编写具体公式内容

```typst
// 标准公式编写方式
$
  -rho overline(u'_i u'_j) = 2 mu_T S_(i j) - frac(2, 3) rho k delta_(i j)
$ <eq:reynolds-stress>

// 使用工具函数简化符号
$
  #reynolds-stress-symbol("i", "j") = 2 #mu-t-symbol() #strain-rate-symbol("i", "j") - frac(2, 3) rho #k-symbol() #kronecker("i", "j")
$ <eq:reynolds-closure>
```

### 3. 使用格式化工具函数

```typst
// 公式说明文字
#equation-description[
  其中 #mu-t-symbol() = $C_mu rho k^2 / epsilon$ 为湍流粘性系数。
]

// 数学符号简化
#pdiff("u", "t")  // 偏微分
#pdiff2("u", "x") // 二阶偏微分
#strain-rate-symbol("i", "j") // 应变率张量符号
```

## 可用函数列表

### 初始化函数
- `init-equations()`: 初始化公式编号和样式设置

### 格式设置函数
- `setup-equation-numbering()`: 设置章节.序号格式的编号
- `setup-equation-style()`: 设置公式基本样式

### 数学符号工具函数
- `pdiff(f, x)`: 偏微分符号 ∂f/∂x
- `diff(f, x)`: 全微分符号 df/dx
- `pdiff2(f, x)`: 二阶偏微分符号 ∂²f/∂x²
- `pdiff-mixed(f, x, y)`: 混合偏微分符号 ∂²f/∂x∂y
- `vec(x)`: 向量符号
- `tensor(x)`: 张量符号
- `mat(x)`: 矩阵符号
- `unit-tensor()`: 单位张量符号
- `kronecker(i, j)`: 克罗内克符号

### 流体力学符号函数
- `reynolds-stress-symbol(i, j)`: Reynolds应力符号
- `strain-rate-symbol(i, j)`: 应变率张量符号
- `vorticity-symbol(i, j)`: 涡量张量符号
- `mu-t-symbol()`: 湍流粘性系数符号
- `k-symbol()`: 湍动能符号
- `epsilon-symbol()`: 耗散率符号

### 格式化工具函数
- `numbered-equation(content, label)`: 带编号的公式块
- `equation-group(..equations)`: 公式组
- `equation-description(content)`: 公式说明文字格式
- `restore-paragraph()`: 恢复段落首行缩进

### 模板函数
- `continuity-template(vars)`: 连续性方程模板
- `momentum-template(vars)`: 动量方程模板
- `transport-template(phi, source)`: 输运方程通用模板

## 公式编号格式

公式会自动按照 "(章节号.序号)" 的格式编号，例如：
- 第2章的第1个公式: (2.1)
- 第2章的第2个公式: (2.2)
- 第3章的第1个公式: (3.1)

## 使用示例

### 完整的章节示例

```typst
#import "../settings/heading.typ": *
#import "../settings/equation.typ": *

#init-equations()

#new-chapter()
#chapter[第三章 数值方法]

#section[湍流模型]

本研究采用Realizable k-ε湍流模型。Reynolds应力封闭为：

$
  #reynolds-stress-symbol("i", "j") = 2 #mu-t-symbol() #strain-rate-symbol("i", "j") - frac(2, 3) rho #k-symbol() #kronecker("i", "j")
$ <eq:reynolds-closure>

#equation-description[
  #mu-t-symbol() = $C_mu rho k^2 / epsilon$ 为湍流粘性系数，#strain-rate-symbol("i", "j") 为应变率张量。
]

湍动能和耗散率的输运方程为：

$
  #pdiff("k", "t") + u_j #pdiff("k", "x_j") = #pdiff("", "x_j") ( frac(nu_T, sigma_k) #pdiff("k", "x_j") ) + G_k - epsilon
$ <eq:k-equation>

$
  #pdiff("epsilon", "t") + u_j #pdiff("epsilon", "x_j") = #pdiff("", "x_j") ( frac(nu_T, sigma_epsilon) #pdiff("epsilon", "x_j") ) + frac(epsilon, k) ( C_1 G_k - C_2 epsilon )
$ <eq:epsilon-equation>

数值求解方面，采用SIMPLE算法求解压力-速度耦合...
```

### 自定义公式示例

```typst
对于温度方程：

$
  frac(partial T, partial t) + u_j frac(partial T, partial x_j) = frac(partial, partial x_j) ( frac(alpha_T, sigma_T) frac(partial T, partial x_j) ) + S_T
$ <eq:temperature>

其中 $T$ 为温度，$alpha_T$ 为湍流热扩散系数。
```

## 扩展和自定义

如果需要添加新的公式函数，可以在 `settings/equation.typ` 文件中添加：

```typst
// 添加新的公式函数
#let your-custom-equation() = {
  $your-equation-here$
}

// 添加新的符号函数
#let your-symbol(x) = $your-symbol-definition$
```

## 注意事项

1. 确保在每个使用公式的章节文件开头调用 `#init-equations()`
2. 公式标签使用 `<eq:name>` 格式，便于引用
3. 复杂公式建议创建专用函数，简单公式可以直接写在章节中
4. 修改公式样式时，只需要修改 `settings/equation.typ` 文件

## 文件结构

```
settings/
├── equation.typ                 # 主要的公式设置文件
├── equation_usage_examples.typ  # 使用示例
└── README_equation_system.md    # 本说明文件
```

这个系统让您可以专注于内容创作，而不需要担心公式格式的一致性问题。

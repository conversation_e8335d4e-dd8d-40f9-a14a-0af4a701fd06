// ================================================================
//                    公式设置文件 (Equation Settings)
// ================================================================

// --- 公式编号设置 ---
// 设置公式编号为章节.序号格式 (如: (2.1), (2.2))
#let setup-equation-numbering() = {
  set math.equation(numbering: n => {
    let chapter-num = counter(heading).get().at(0)
    "(" + str(chapter-num) + "." + str(n) + ")"
  })
}

// --- 公式样式设置 ---
#let setup-equation-style() = {
  // 设置公式的基本样式
  set math.equation(
    block: true,
    supplement: [公式]
  )
}

// --- 常用数学符号定义 ---
// 偏微分符号
#let pdiff(f, x) = $frac(partial #f, partial #x)$

// 全微分符号  
#let diff(f, x) = $frac(d #f, d #x)$

// 向量符号
#let vec(x) = $bold(#x)$

// 张量符号
#let tensor(x) = $bold(#x)$

// 矩阵符号
#let mat(x) = $bold(#x)$

// --- 流体力学常用符号 ---
// Reynolds应力
#let reynolds-stress(i, j) = $-rho overline(u'_#i u'_#j)$

// 应变率张量
#let strain-rate(i, j) = $S_(#i #j) = frac(1, 2) (partial u_#i / partial x_#j + partial u_#j / partial x_#i)$

// 湍流粘性系数
#let turbulent-viscosity() = $mu_T = C_mu rho k^2 / epsilon$

// 湍动能生成项
#let turbulent-production() = $G_k = 2 mu_T S_(i j) S_(i j)$

// --- 常用公式模板 ---
// k-epsilon湍流模型的k方程
#let k-equation() = {
  $frac(partial k, partial t) + u_j frac(partial k, partial x_j) = frac(partial, partial x_j) ( frac(nu_T, sigma_k) frac(partial k, partial x_j) ) + G_k - epsilon$
}

// k-epsilon湍流模型的epsilon方程
#let epsilon-equation() = {
  $frac(partial epsilon, partial t) + u_j frac(partial epsilon, partial x_j) = frac(partial, partial x_j) ( frac(nu_T, sigma_epsilon) frac(partial epsilon, partial x_j) ) + frac(epsilon, k) ( C_1 G_k - C_2 epsilon )$
}

// Reynolds应力方程
#let reynolds-stress-equation() = {
  $-rho overline(u'_i u'_j) = 2 mu_T S_(i j) - frac(2, 3) rho k delta_(i j)$
}

// --- 公式组合函数 ---
// 完整的k-epsilon湍流模型
#let k-epsilon-model() = {
  [
    其中，#turbulent-viscosity() 为湍流粘性系数，#strain-rate("i", "j") 为应变率张量。$k$与$epsilon$的输运方程写为：
    
    #k-equation() <eq:k-transport>
    
    #epsilon-equation() <eq:epsilon-transport>
    
    其中湍动能生成项 #turbulent-production()。
  ]
}

// --- 公式后段落恢复函数 ---
// 确保公式后的段落恢复正常的首行缩进
#let restore-paragraph() = {
  // 这个函数可以在公式后调用，确保段落格式正确
  par(first-line-indent: 2em)[]
}

// --- 带标签的公式函数 ---
#let labeled-equation(eq, label) = {
  [$ eq $] + label
}

// --- 公式引用函数 ---
#let eq-ref(label) = {
  [#label]
}

// --- 初始化函数 ---
// 在章节开始时调用，设置所有公式相关的样式
#let init-equations() = {
  setup-equation-numbering()
  setup-equation-style()
}

// ================================================================
//                    公式格式设置文件 (Equation Format Settings)
// ================================================================
// 本文件只负责公式的格式设定和工具函数，不包含具体公式内容
// 具体公式内容在各章节中单独编写，保证灵活性和独立性

// --- 公式编号设置 ---
// 设置公式编号为章节.序号格式 (如: (2.1), (2.2), (2.3))
// 在章节文件中仍然可以使用简单的 (1), (2), (3) 来引用
#let setup-equation-numbering() = {
  set math.equation(numbering: n => {
    let chapter-num = counter(heading).get().at(0)
    "(" + str(chapter-num) + "." + str(n) + ")"
  })
}

// --- 公式样式设置 ---
#let setup-equation-style() = {
  // 设置公式的基本样式
  set math.equation(
    block: true,
    supplement: [公式]
  )
}

// --- 常用数学符号工具函数 ---
// 偏微分符号
#let pdiff(f, x) = $frac(partial #f, partial #x)$

// 全微分符号
#let diff(f, x) = $frac(d #f, d #x)$

// 二阶偏微分
#let pdiff2(f, x) = $frac(partial^2 #f, partial #x^2)$

// 混合偏微分
#let pdiff-mixed(f, x, y) = $frac(partial^2 #f, partial #x partial #y)$

// 向量符号
#let vec(x) = $bold(#x)$

// 张量符号
#let tensor(x) = $bold(#x)$

// 矩阵符号
#let mat(x) = $bold(#x)$

// 单位张量
#let unit-tensor() = $bold(I)$

// 克罗内克符号
#let kronecker(i, j) = $delta_(#i #j)$

// --- 流体力学常用符号工具函数 ---
// Reynolds应力符号
#let reynolds-stress-symbol(i, j) = $-rho overline(u'_#i u'_#j)$

// 应变率张量符号
#let strain-rate-symbol(i, j) = $S_(#i #j)$

// 涡量张量符号
#let vorticity-symbol(i, j) = $Omega_(#i #j)$

// 湍流粘性系数符号
#let mu-t-symbol() = $mu_T$

// 湍动能符号
#let k-symbol() = $k$

// 耗散率符号
#let epsilon-symbol() = $epsilon$

// --- 公式格式化工具函数 ---
// 带编号的公式块（推荐使用）
#let eq(content, label: none) = {
  if label != none {
    [$ #content $] + [<#label>]
  } else {
    [$ #content $]
  }
}

// 无编号公式
#let eq-unnumbered(content) = {
  [$ #content $]
}

// 带编号的公式块（兼容旧版本）
#let numbered-equation(content, label: none) = {
  if label != none {
    [$ content $] + label
  } else {
    [$ content $]
  }
}

// 公式组（多个相关公式）
#let equation-group(..equations) = {
  for eq in equations.pos() {
    eq
    v(0.5em)
  }
}

// 公式说明文字格式
#let equation-description(content) = {
  set par(first-line-indent: 0em)
  [其中，#content]
}

// 公式后恢复段落格式
#let restore-paragraph() = {
  set par(first-line-indent: 2em)
  []
}

// --- 常用数学表达式模板 ---
// 连续性方程模板
#let continuity-template(vars) = $frac(partial #vars.rho, partial t) + frac(partial (#vars.rho #vars.u), partial #vars.x) = 0$

// 动量方程模板
#let momentum-template(vars) = $frac(partial (#vars.rho #vars.u), partial t) + frac(partial (#vars.rho #vars.u #vars.v), partial #vars.x) = -frac(partial #vars.p, partial #vars.x) + frac(partial #vars.tau, partial #vars.x)$

// 输运方程通用模板
#let transport-template(phi, source: none) = {
  let base = $frac(partial #phi, partial t) + u_j frac(partial #phi, partial x_j) = frac(partial, partial x_j) ( Gamma frac(partial #phi, partial x_j) )$
  if source != none {
    $base + #source$
  } else {
    base
  }
}

// --- 初始化函数 ---
// 在章节开始时调用，设置所有公式相关的样式
#let init-equations() = {
  setup-equation-numbering()
  setup-equation-style()
}
